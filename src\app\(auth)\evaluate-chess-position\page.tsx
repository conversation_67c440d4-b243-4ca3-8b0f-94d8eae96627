'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Header } from '@/components/header';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/common/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from 'sonner';
import { Loader2, Brain, ChevronRight, RotateCcw } from 'lucide-react';
import { ChessBoard } from '@/components/chess';
import { parseChessPosition, STARTING_POSITION_FEN } from '@/lib/chess-utils';
import type {
  EvaluatePositionRequest,
  EvaluatePositionResponse,
  EvaluatePositionError,
  ChessPosition,
} from '@/types/chess';

const formSchema = z.object({
  position: z
    .string()
    .min(10, 'Position description must be at least 10 characters')
    .max(2000, 'Position description must be less than 2000 characters'),
});

type FormData = z.infer<typeof formSchema>;

const EvaluateChessPositionPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [evaluation, setEvaluation] = useState<string | null>(null);
  const [chessPosition, setChessPosition] = useState<ChessPosition>({
    fen: '',
    isValid: false,
  });
  const [boardOrientation, setBoardOrientation] = useState<'white' | 'black'>(
    'white',
  );

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      position: STARTING_POSITION_FEN,
    },
  });

  // Watch the position field to update chess board
  const positionValue = form.watch('position');

  useEffect(() => {
    if (positionValue) {
      const parsed = parseChessPosition(positionValue);
      setChessPosition(parsed);
    } else {
      setChessPosition({ fen: '', isValid: false });
    }
  }, [positionValue]);

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    setEvaluation(null);

    try {
      const response = await fetch('/api/evaluate-position', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          position: data.position,
        } as EvaluatePositionRequest),
      });

      if (!response.ok) {
        const errorData: EvaluatePositionError = await response.json();
        throw new Error(errorData.error || 'Failed to evaluate position');
      }

      const result: EvaluatePositionResponse = await response.json();
      setEvaluation(result.evaluation);
      toast.success('Position evaluated successfully!');
    } catch (error) {
      console.error('Error evaluating position:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to evaluate position',
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Header />
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="space-y-8">
          {/* Header Section */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-2">
              <Brain className="h-8 w-8 text-primary" />
              <h1 className="text-3xl font-bold">Chess Position Evaluator</h1>
            </div>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Get AI-powered analysis of chess positions. Describe a position in
              FEN notation, and receive detailed evaluation including tactical
              and strategic insights.
            </p>
          </div>

          {/* Desktop Layout: Board on left, content on right */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column: Chess Board */}
            <div className="order-1">
              {chessPosition.isValid && (
                <div className="bg-card rounded-lg border p-6 sticky top-4">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold flex items-center gap-2">
                      <Brain className="h-5 w-5 text-primary" />
                      Chess Position
                    </h2>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setBoardOrientation(
                          boardOrientation === 'white' ? 'black' : 'white',
                        )
                      }
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Flip Board
                    </Button>
                  </div>
                  <div className="flex justify-center">
                    <ChessBoard
                      fen={chessPosition.fen}
                      orientation={boardOrientation}
                      viewOnly={true}
                      className="w-full max-w-lg"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground mt-4 text-center">
                    FEN:{' '}
                    <code className="bg-muted px-2 py-1 rounded text-xs">
                      {chessPosition.fen}
                    </code>
                  </p>
                </div>
              )}
            </div>

            {/* Right Column: Evaluation, Form, and Help */}
            <div className="space-y-6">
              {/* Results Section */}
              {evaluation && (
                <div className="bg-card rounded-lg border p-6">
                  <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Brain className="h-5 w-5 text-primary" />
                    AI Evaluation
                  </h2>
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <div
                      className="whitespace-pre-wrap text-sm leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: evaluation
                          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                          .replace(/\*(.*?)\*/g, '<em>$1</em>')
                          .replace(/^(\d+\.\s.*?)$/gm, '<strong>$1</strong>')
                          .replace(/\n/g, '<br>'),
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Form Section */}
              <div className="bg-card rounded-lg border p-6">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="position"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-semibold">
                            Chess Position
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              label=""
                              placeholder="Enter a chess position in FEN notation (e.g., 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1')"
                              minRows={1}
                              maxRows={8}
                              maxLength={2000}
                              showCount
                              className="resize-none"
                            />
                          </FormControl>
                          <FormDescription>
                            You can use FEN notation for precise positions.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button
                        type="submit"
                        disabled={isLoading || !form.formState.isValid}
                        className="flex-1 sm:flex-none"
                        size="lg"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Analyzing Position...
                          </>
                        ) : (
                          <>
                            <Brain className="mr-2 h-4 w-4" />
                            Evaluate Position
                            <ChevronRight className="ml-2 h-4 w-4" />
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </div>

              {/* Help Section */}
              <div className="bg-muted/50 rounded-lg p-6">
                <h3 className="font-semibold mb-3">How to use:</h3>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>
                    • <strong>FEN Notation:</strong> Use standard FEN for
                    precise position analysis - the chess board will
                    automatically display when valid FEN is detected
                  </li>
                  <li>
                    • <strong>Visual Board:</strong> When you enter a valid FEN
                    string, a chess board will appear showing the position. Use
                    the "Flip Board" button to change orientation
                  </li>
                  <li>
                    • <strong>Analysis:</strong> Get comprehensive evaluation
                    including tactics, strategy, and move recommendations
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default EvaluateChessPositionPage;
